<template>
  <div class="user-info">
    <div class="box1-avatar-bg" @click="goUserInfo">
      <img :src="userInfo.avatar" alt="" class="navatar" />
    </div>
    <div class="box1">
      <div class="user-name-row">
        <div class="user-name">{{ userInfo.name }}</div>
        <button class="edit-btn" @click="goEdit">编辑资料</button>
        <button class="reset-pwd-btn" @click="goResetPassword">修改密码</button>
        <button class="logout-btn" @click="handleLogout">退出登录</button>
      </div>
      <div class="user-tags">
        <div class="tag VIP" v-if="userInfo.vipLevel">
          <img :src="VIP" alt="" class="tag-img" />
          <span>{{ userInfo.vipLevel }}</span>
        </div>
        <div class="tag Coupon">
          <img :src="Coupon" alt="" class="tag-img" />
          <span>大学生优惠</span>
        </div>
        <!-- 显示用户角色 -->
        <div class="tag Role" v-if="userStore.userInfo?.role">
          <span>{{ getRoleDisplayName(userStore.userInfo.role) }}</span>
        </div>
      </div>
    </div>
    <div class="my-wallet">
      <div class="my-wallet-title-row">
        <span class="my-wallet-title">我的钱包</span>
        <img
          :src="showEye ? eyeShow : eyeHide"
          class="my-wallet-eye-img"
          @click="toggleEye"
          alt="eye"
        />
      </div>
      <div class="my-wallet-items">
        <div class="my-wallet-item clickable-item" @click="goCoupon">
          <div class="wallet-num">{{ userWallet.coupon }}</div>
          <div class="wallet-label">优惠券</div>
        </div>
        <div class="my-wallet-item">
          <div class="wallet-num">{{ userWallet.jingdou }}</div>
          <div class="wallet-label">鲸豆</div>
        </div>
        <div class="my-wallet-item">
          <div class="wallet-num">{{ showEye ? userWallet.balance : '****' }}</div>
          <div class="wallet-label">余额</div>
        </div>
        <div class="my-wallet-item">
          <div class="wallet-num">{{ showEye ? userWallet.whale : '****' }}</div>
          <div class="wallet-label">鲸条本月待还</div>
        </div>
        <div class="my-wallet-item">
          <div class="wallet-num">{{ showEye ? userWallet.yesterday : '****' }}</div>
          <div class="wallet-label">小鲸库昨日收益</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import navatar from '@/assets/images/my/navatar.jpg'
import VIP from '@/assets/images/my/银牌会员.svg'
import Coupon from '@/assets/images/my/大学生优惠.svg'
import eyeShow from '@/assets/images/my/眼睛_显示_o.svg'
import eyeHide from '@/assets/images/my/眼睛_隐藏_o.svg'
import { userInfoService } from '@/api/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import useUserInfoStore from '@/stores/userInfo'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const showEye = ref(true)
const userInfoStore = useUserInfoStore()
const userStore = useUserStore()

const userInfo = ref({
  name: '',
  avatar: '',
  vipLevel: '',
  isStudent: false
})

const userWallet = ref({
  coupon: 6, // 默认显示6张优惠券
  jingdou: 1280, // 默认显示1280鲸豆
  balance: '268.50', // 默认余额
  whale: '1,250.00', // 默认鲸条本月待还
  yesterday: '12.35' // 默认小鲸库昨日收益
})

// 获取用户信息
const getUserInfo = async () => {
  try {
    const result = await userInfoService()
    if (result.code === 0 && result.data) {
      // 更新用户信息
      userInfo.value = {
        name: result.data.username || '未设置用户名',
        avatar: result.data.avatar || navatar,
        vipLevel: result.data.vipLevel || '普通会员',
        isStudent: result.data.isStudent || false
      }

      // 更新钱包信息 - 由于后端暂时没有钱包相关字段，使用模拟数据
      userWallet.value = {
        coupon: result.data.coupon || 6, // 模拟6张优惠券
        jingdou: result.data.jingdou || 1280, // 模拟1280鲸豆
        balance: result.data.balance || '268.50', // 模拟余额
        whale: result.data.whale || '1,250.00', // 模拟鲸条本月待还
        yesterday: result.data.yesterday || '12.35' // 模拟小鲸库昨日收益
      }

      // 存储到pinia
      userInfoStore.setInfo({
        ...result.data,
        avatar: result.data.avatar || navatar  // 确保头像有默认值
      })
    } else {
      ElMessage.error('获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

function toggleEye() {
  showEye.value = !showEye.value
}

// 获取角色显示名称
function getRoleDisplayName(role) {
  const roleMap = {
    'ADMIN': '管理员',
    'MANUFACTURER': '生产商',
    'CUSTOMER': '普通用户'
  }
  return roleMap[role] || '普通用户'
}

function goEdit() {
  router.push('/user/info')
}

function goUserInfo() {
  router.push('/user/info')
}

function goResetPassword() {
  router.push('/user/reset-password')
}

function goCoupon() {
  router.push('/wallet/coupon')
}

function handleLogout() {
  ElMessageBox.confirm(
    '确定要退出登录吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 清除用户信息
      userInfoStore.removeInfo()
      // 清除 token 和用户信息
      userStore.logout()
      // 清除本地存储
      localStorage.removeItem('user-store')
      localStorage.removeItem('userInfo-store')
      // 跳转到登录页
      router.push('/login')
      ElMessage.success('已退出登录')
    })
    .catch(() => {
      ElMessage.info('已取消退出')
    })
}

// 组件挂载时获取用户信息
onMounted(() => {
  getUserInfo()
})
</script>

<style scoped>
.user-info {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 32px 24px 24px 24px;
  background: linear-gradient(135deg, #2193b0 0%, #1565c0 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(33, 147, 176, 0.2);
  min-width: 260px;
  gap: 28px;
  position: relative;
  overflow: hidden;
}

.user-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
  pointer-events: none;
}

.box1-avatar-bg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 76px;
  height: 76px;
  background: rgba(255,255,255,0.12);
  border-radius: 50%;
  cursor: pointer;
  margin-right: 0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.box1-avatar-bg:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(255,255,255,0.3);
  background: rgba(255,255,255,0.2);
}

.box1 {
  border: none;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 180px;
  flex-shrink: 0;
  margin-left: 0;
}

.navatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 2px solid rgba(255,255,255,0.8);
  box-shadow: 0 2px 12px rgba(178,235,242,0.4);
  object-fit: cover;
  transition: all 0.3s ease;
}

.user-name-row {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: flex-start;
  margin-bottom: 12px;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: white;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.edit-btn {
  background: rgba(255,255,255,0.9);
  color: #2193b0;
  border: none;
  border-radius: 20px;
  font-size: 13px;
  padding: 4px 16px;
  cursor: pointer;
  margin-left: 12px;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.edit-btn:hover {
  background: #fff;
  color: #1565c0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.reset-pwd-btn {
  background: rgba(255,255,255,0.9);
  color: #ff9800;
  border: none;
  border-radius: 20px;
  font-size: 13px;
  padding: 4px 16px;
  cursor: pointer;
  margin-left: 12px;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.reset-pwd-btn:hover {
  background: #fff;
  color: #f57c00;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.logout-btn {
  background: rgba(255,255,255,0.9);
  color: #ff4d4f;
  border: none;
  border-radius: 20px;
  font-size: 13px;
  padding: 4px 16px;
  cursor: pointer;
  margin-left: 12px;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.logout-btn:hover {
  background: #fff;
  color: #ff7875;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.user-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  display: flex;
  align-items: center;
  background: rgba(255,255,255,0.95);
  border-radius: 20px;
  padding: 4px 12px 4px 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  font-size: 12px;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.12);
}

.tag-img {
  width: 18px;
  height: 18px;
  margin-right: 6px;
}

.VIP span,
.Coupon span,
.Role span {
  font-size: 12px;
  font-weight: 500;
  color: #2193b0;
}

.Role {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.Role span {
  color: white;
}

.my-wallet {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(33, 147, 176, 0.15);
  padding: 20px 24px 16px 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.my-wallet:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(33, 147, 176, 0.2);
}

.my-wallet-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(33, 147, 176, 0.1);
}

.my-wallet-title {
  font-size: 17px;
  font-weight: 600;
  color: #1565c0;
  letter-spacing: 0.5px;
}

.my-wallet-eye-img {
  width: 20px;
  height: 20px;
  cursor: pointer;
  margin-left: 8px;
  vertical-align: middle;
  transition: transform 0.3s ease;
}

.my-wallet-eye-img:hover {
  transform: scale(1.1);
}

.my-wallet-items {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 0;
}

.my-wallet-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 70px;
  padding: 12px 0;
  flex: 1;
  border-right: 1px solid rgba(33, 147, 176, 0.1);
  transition: all 0.3s ease;
}

.my-wallet-item:last-child {
  border-right: none;
}

.my-wallet-item:hover {
  background: rgba(33, 147, 176, 0.03);
}

.wallet-num {
  font-size: 20px;
  font-weight: 600;
  color: #2193b0;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.my-wallet-item:hover .wallet-num {
  color: #1565c0;
  transform: scale(1.05);
}

.wallet-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
  letter-spacing: 0.5px;
  transition: color 0.3s ease;
}

.my-wallet-item:hover .wallet-label {
  color: #2193b0;
}

.clickable-item {
  cursor: pointer;
}

.clickable-item:hover {
  background: rgba(33, 147, 176, 0.08) !important;
  transform: translateY(-1px);
}

@media (max-width: 900px) {
  .user-info {
    padding: 24px 16px 20px 16px;
    gap: 20px;
  }

  .my-wallet {
    padding: 16px 20px 12px 20px;
  }

  .my-wallet-item {
    min-width: 60px;
    padding: 10px 0;
  }

  .wallet-num {
    font-size: 18px;
  }
}

@media (max-width: 700px) {
  .user-info {
    flex-direction: column;
    gap: 16px;
    padding: 20px 16px;
  }

  .box1 {
    width: 100%;
    align-items: center;
  }

  .user-name-row {
    justify-content: center;
  }

  .my-wallet {
    width: 100%;
    padding: 16px;
  }

  .my-wallet-items {
    flex-wrap: wrap;
  }

  .my-wallet-item {
    min-width: 45%;
    border-right: none;
    border-bottom: 1px solid rgba(33, 147, 176, 0.1);
    padding: 12px 0;
  }

  .my-wallet-item:nth-child(2n) {
    border-left: 1px solid rgba(33, 147, 176, 0.1);
  }

  .my-wallet-item:last-child,
  .my-wallet-item:nth-last-child(2) {
    border-bottom: none;
  }
}
</style>