package com.itheima.springbootcd.controller;

import com.itheima.springbootcd.annotation.RequireRole;
import com.itheima.springbootcd.enums.UserRole;
import com.itheima.springbootcd.pojo.Category;
import com.itheima.springbootcd.pojo.Result;
import com.itheima.springbootcd.service.ICategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@RestController
@RequestMapping("/category")
public class CategoryController {
    @Autowired
    private ICategoryService categoryService;
    @PostMapping
    @RequireRole(UserRole.ADMIN)
    public Result<String> add(@RequestBody @Validated(Category.Add.class) Category category){
        categoryService.add(category);
        return Result.success();
    }
    @GetMapping
    public Result<List<Category>> list(){
        List<Category> cs = categoryService.list();
        return Result.success(cs);
    }
    @GetMapping("/detail")
    public Result<Category> detail(Integer id){
        Category c = categoryService.findById(id);
        return Result.success(c);
    }
    @PutMapping
    @RequireRole(UserRole.ADMIN)
    public Result<String> update(@RequestBody @Validated(Category.Update.class) Category category){
        categoryService.update(category);
        return Result.success();
    }
    @PostMapping("/delete")
    @RequireRole(UserRole.ADMIN)
    public Result<String> delete(@RequestBody Category category) {
        categoryService.delete(category.getCategoryId());
        return Result.success();
    }
    @GetMapping("/search")
    public Result<List<Category>> search(@RequestParam String categoryName){
        List<Category> categories = categoryService.search(categoryName);
        return Result.success(categories);
    }
}
