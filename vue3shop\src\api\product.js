import request from '@/utils/request'
import { useUserStore } from '@/stores/user'
//分类列表
export const productCategoryListService = () => {
  const userStore = useUserStore()
  return request.get('/category', {
    headers: {
      'Authorization': userStore.token
    }
  })
}

// 添加分类
export const productCategoryAddService = (categoryData) => {
  const userStore = useUserStore()
  return request.post('/category', categoryData, {
    headers: {
      'Authorization': userStore.token
    }
  })
}
// 提供调用获取商品列表接口的函数
export const productListService = () => {
  return request({
      url: '/product/list',
      method: 'get',
      headers: {
          'Content-Type': 'application/json' // 对于GET请求，通常使用application/json或不指定Content-Type
      }
  })
}

// 提供调用获取商品详情接口的函数
export const productDetailService = (productId) => {
  return request({
      url: `/product/selectById/${productId}`,
      method: 'get',
      headers: {
          'Content-Type': 'application/json'
      }
  })
}
//产品分类修改
export const productCategoryUpdateService = (categoryData) => {
  const userStore = useUserStore()
  return request.put('/category', categoryData, {
    headers: {
      'Authorization': userStore.token
    }
  })
}
//产品分类删除
export const productCategoryDeleteService = (categoryId) => {
  return request.post('/category/delete', { categoryId })
}

// 添加商品
export const productAddService = (productData) => {
  const userStore = useUserStore()
  return request.post('/product/add', productData, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}
//通过分类名称搜索分类
export const productCategorySearchService = (categoryName) => {
  const userStore = useUserStore()
  return request.get('/category/search', {
    params: { categoryName },
    headers: {
      'Authorization': userStore.token
    }
  })
}

// 获取分类详情
export const productCategoryDetailService = (categoryId) => {
  const userStore = useUserStore()
  return request.get('/category/detail', {
    params: { id: categoryId },
    headers: {
      'Authorization': userStore.token
    }
  })
}

// 根据分类ID获取商品列表
export const productListByCategoryService = (categoryId) => {
  return request.get(`/product/listByCategoryId/${categoryId}`)
}

// 根据生产商ID获取商品列表
export const productListByManufacturerService = (manufacturerId) => {
  return request.get(`/product/listByManufacturerId/${manufacturerId}`)
}

// 更新商品
export const productUpdateService = (productId, productData) => {
  const userStore = useUserStore()
  return request.put(`/product/update/${productId}`, productData, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}

// 删除商品
export const productDeleteService = (productId) => {
  const userStore = useUserStore()
  return request.delete(`/product/delete/${productId}`, {}, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': userStore.token
    }
  })
}

//修改头像
export const userAvatarUpdateService = (avatarUrl)=>{
    const params = new URLSearchParams()
    params.append('avatarUrl',avatarUrl)
    return request.patch('/product/updateAvatar',params)
}

/**
 * 获取当前用户可管理的商品列表（根据角色过滤）
 * 管理员可以看到所有商品，生产商只能看到自己的商品
 */
export const productListForManageService = () => {
  const userStore = useUserStore()
  return request.get('/product/listForManage', {
    headers: {
      'Authorization': userStore.token
    }
  })
}