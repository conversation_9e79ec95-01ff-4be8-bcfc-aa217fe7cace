import { createRouter, createWebHistory } from "vue-router";
import { useUserStore } from "@/stores/user";
import { ElMessage } from 'element-plus';

const routes = [
  {
    path: "/login",
    name: "login",
    component: () => import("../views/RtLogin.vue"),
  },
  {
    path: "/",
    name: "home",
    component: () => import("../views/RtHome.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/category",
    name: "category",
    component: () => import("../views/product/ProductCategory.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/category/:id",
    name: "CategoryDetail",
    component: () => import("../views/product/CategoryDetail.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/user",
    component: () => import("../views/user/RtIndex.vue"),
  },
  {
    path: "/collect",
    component: () => import("../views/user/RtCollectList.vue"),
  },
  {
    path: "/address/list",
    component: () => import("../views/user/RtAddressList.vue"),
  },
  {
    path: "/address/edit",
    component: () => import("../views/user/RtEdit.vue"),
  },
  {
    path: "/cart",
    name: "Cart",
    component: () => import("@/views/cart/RtCart.vue"),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: "/order/list",
    component: () => import("../views/user/RtOrderList.vue"),
  },
  {
    path: "/product/:id",
    name: "ProductDetail",
    component: () => import("../views/product/ProductDetail.vue"),
  },
  {
    path: "/user/info",
    component: () => import("../views/user/UserInfo.vue"),
  },
  {
    path: "/user/avatar",
    component: () => import("../views/user/UserAvatar.vue"),
  },
  {
    path: "/user/reset-password",
    name: "UserResetPassword",
    component: () => import("../views/user/UserResetPassword.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/product/manage",
    name: "ProductManage",
    component: () => import("../views/product/ProductManage.vue"),
    meta: {
      requiresAuth: true,
      requiresRole: ['MANUFACTURER', 'ADMIN']
    },
  },
  {
    path: "/diy-computer",
    name: "DiyComputer",
    component: () => import("../views/nav/designDiyComputer.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/waishe-tuijian",
    name: "WaiSheTuijian",
    component: () => import("../views/nav/waiSheTuijian.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/wallet/coupon",
    name: "Coupon",
    component: () => import("../views/wallet/RtCoupon.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/manufacturer",
    name: "ManufacturerList",
    component: () => import("../views/manufacturer/ManufacturerList.vue"),
  },
  {
    path: "/manufacturer/:id/detail",
    name: "ManufacturerDetail",
    component: () => import("../views/manufacturer/ManufacturerDetail.vue"),
  },
  {
    path: "/manufacturer/manage",
    name: "ManufacturerManage",
    component: () => import("../views/manufacturer/ManufacturerManage.vue"),
    meta: {
      requiresAuth: true,
      requiresRole: ['ADMIN']
    },
  },
  {
    path: "/test/permission",
    name: "PermissionTest",
    component: () => import("../views/test/PermissionTest.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("../components/Rt404Page.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore();

  // 如果是登录页面，直接放行
  if (to.path === "/login") {
    // 如果已经登录，重定向到首页
    if (userStore.token) {
      next("/");
      return;
    }
    next();
    return;
  }

  // 检查是否已登录
  if (!userStore.token) {
    console.log("未登录，重定向到登录页");
    // 未登录，重定向到登录页
    next({
      path: "/login",
      query: { redirect: to.fullPath }, // 保存原始目标路径
    });
    return;
  }

  // 检查角色权限
  if (to.meta.requiresRole) {
    const requiredRoles = to.meta.requiresRole;
    if (!userStore.hasRole(requiredRoles)) {
      console.log("权限不足，重定向到首页");
      ElMessage.error('您没有权限访问该页面');
      next("/");
      return;
    }
  }

  // 已登录且有权限，允许访问
  console.log("已登录，允许访问:", to.path);
  next();
});

export default router;
