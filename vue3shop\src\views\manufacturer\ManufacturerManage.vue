<template>
  <div class="manufacturer-manage">
    <RtHeader title="生产商管理" />
    <div class="manufacturer-container">
      <el-card class="manufacturer-card">
        <template #header>
          <div class="header">
            <span class="title">生产商管理</span>
            <div class="extra" v-if="userStore.isAdmin()">
              <el-button type="primary" @click="handleAdd">添加生产商</el-button>
            </div>
          </div>
        </template>

        <el-table :data="manufacturers" style="width: 100%" v-loading="loading">
          <el-table-column label="序号" width="80" type="index" />
          <el-table-column label="Logo" width="100">
            <template #default="{ row }">
              <img 
                :src="row.logoUrl || defaultLogo" 
                :alt="row.manufacturerName"
                class="manufacturer-logo-small"
                @error="handleImageError"
              />
            </template>
          </el-table-column>
          <el-table-column label="生产商名称" prop="manufacturerName">
            <template #default="{ row }">
              <span class="manufacturer-name clickable" @click="goToDetail(row.manufacturerId)">
                {{ row.manufacturerName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="描述" prop="description">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.description || '暂无描述' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="联系方式" prop="contactInfo" />
          <el-table-column label="地址" prop="address" />
          <el-table-column label="创建时间" prop="createTime" width="180" />
          <el-table-column label="操作" width="200" fixed="right" v-if="userStore.isAdmin()">
            <template #default="{ row }">
              <el-button type="primary" :icon="Edit" circle @click="handleEdit(row)" />
              <el-button type="danger" :icon="Delete" circle @click="handleDelete(row)" />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 添加/编辑生产商对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加生产商' : '编辑生产商'"
      width="600px"
    >
      <el-form :model="manufacturerForm" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="生产商名称" prop="manufacturerName">
          <el-input v-model="manufacturerForm.manufacturerName" placeholder="请输入生产商名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="manufacturerForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入生产商描述" 
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactInfo">
          <el-input v-model="manufacturerForm.contactInfo" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="manufacturerForm.address" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="官网" prop="website">
          <el-input v-model="manufacturerForm.website" placeholder="请输入官网地址" />
        </el-form-item>
        <el-form-item label="Logo">
          <el-upload
            ref="uploadRef"
            class="logo-uploader"
            :show-file-list="false"
            :auto-upload="true"
            action="http://localhost:8080/upload"
            name="image"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
          >
            <img v-if="manufacturerForm.logoUrl" :src="manufacturerForm.logoUrl" class="logo-preview" />
            <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-buttons">
            <el-button type="primary" :icon="Plus" size="small" @click="triggerUpload">
              选择Logo
            </el-button>
            <el-tooltip content="建议上传正方形图片，尺寸不小于200x200px" placement="top">
              <el-button type="info" :icon="InfoFilled" size="small">
                提示
              </el-button>
            </el-tooltip>
          </div>
          <div class="upload-tip">只能上传jpg/png文件，且不超过2MB</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <RtTabBar />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Edit, Delete, Plus, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import { 
  manufacturerListService, 
  manufacturerAddService, 
  manufacturerUpdateService,
  manufacturerDeleteService 
} from '@/api/manufacturer'
import defaultLogo from '@/assets/images/default-manufacturer.png'

const userStore = useUserStore()
const router = useRouter()
const manufacturers = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const uploadRef = ref(null)

// 表单数据
const manufacturerForm = ref({
  manufacturerId: null,
  manufacturerName: '',
  description: '',
  contactInfo: '',
  address: '',
  website: '',
  logoUrl: ''
})

// 表单验证规则
const rules = {
  manufacturerName: [
    { required: true, message: '请输入生产商名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入生产商描述', trigger: 'blur' }
  ]
}

// 获取生产商列表
const getManufacturerList = async () => {
  try {
    loading.value = true
    const result = await manufacturerListService()
    
    if (result && result.code === 0) {
      manufacturers.value = result.data || []
    } else {
      ElMessage.error(result?.message || '获取生产商列表失败')
    }
  } catch (error) {
    console.error('获取生产商列表失败:', error)
    ElMessage.error('获取生产商列表失败')
  } finally {
    loading.value = false
  }
}

// 跳转到生产商详情
const goToDetail = (manufacturerId) => {
  router.push(`/manufacturer/${manufacturerId}/detail`)
}

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.src = defaultLogo
}

// 添加生产商
const handleAdd = () => {
  if (!userStore.checkPermission('ADMIN', '只有管理员可以添加生产商')) return
  dialogType.value = 'add'
  manufacturerForm.value = {
    manufacturerId: null,
    manufacturerName: '',
    description: '',
    contactInfo: '',
    address: '',
    website: '',
    logoUrl: ''
  }
  dialogVisible.value = true
}

// 编辑生产商
const handleEdit = (row) => {
  if (!userStore.checkPermission('ADMIN', '只有管理员可以编辑生产商')) return
  dialogType.value = 'edit'
  manufacturerForm.value = {
    manufacturerId: row.manufacturerId,
    manufacturerName: row.manufacturerName,
    description: row.description || '',
    contactInfo: row.contactInfo || '',
    address: row.address || '',
    website: row.website || '',
    logoUrl: row.logoUrl || ''
  }
  dialogVisible.value = true
}

// 删除生产商
const handleDelete = (row) => {
  if (!userStore.checkPermission('ADMIN', '只有管理员可以删除生产商')) return
  ElMessageBox.confirm(
    `确定要删除生产商"${row.manufacturerName}"吗？`,
    '温馨提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const result = await manufacturerDeleteService(row.manufacturerId)
      if (result && result.code === 0) {
        ElMessage.success('删除成功')
        await getManufacturerList()
      } else {
        ElMessage.error(result?.message || '删除失败')
      }
    } catch (error) {
      console.error('删除生产商失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        let result
        if (dialogType.value === 'add') {
          result = await manufacturerAddService(manufacturerForm.value)
        } else {
          result = await manufacturerUpdateService(manufacturerForm.value)
        }

        if (result && result.code === 0) {
          ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
          dialogVisible.value = false
          await getManufacturerList()
        } else {
          ElMessage.error(result?.message || '操作失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('操作失败')
      }
    }
  })
}

// 触发文件选择
const triggerUpload = () => {
  if (uploadRef.value) {
    uploadRef.value.$el.querySelector('input').click()
  }
}

// 上传图片成功时的处理
const handleUploadSuccess = (response) => {
  console.log('上传响应:', response)
  if (response.code === 0) {
    manufacturerForm.value.logoUrl = response.data
    ElMessage.success('Logo上传成功')
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

// 上传图片失败时的处理
const handleUploadError = (error) => {
  console.error('上传失败:', error)
  ElMessage.error('上传失败，请重试')
}

// 上传前验证
const beforeUpload = (file) => {
  console.log('准备上传文件:', file)
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

onMounted(async () => {
  // 检查权限
  if (!userStore.checkPermission('ADMIN', '您没有权限访问生产商管理页面')) {
    router.push('/')
    return
  }

  await getManufacturerList()
})
</script>

<style lang="scss" scoped>
.manufacturer-manage {
  min-height: 100vh;
  background: #f6f8fa;
}

.manufacturer-container {
  padding: 20px;
  margin-top: 20px;
}

.manufacturer-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px #e3f2fd;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-size: 18px;
      font-weight: bold;
      color: #1565c0;
    }
  }
}

.manufacturer-logo-small {
  width: 50px;
  height: 50px;
  object-fit: contain;
  border-radius: 5px;
  border: 1px solid #e4e7ed;
}

.manufacturer-name {
  font-weight: 500;
  color: #2193b0;
  
  &.clickable {
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
      color: #1565c0;
      text-decoration: underline;
    }
  }
}

:deep(.el-table) {
  --el-table-border-color: #e3f2fd;
  --el-table-header-bg-color: #f8fafc;

  th {
    font-weight: bold;
    color: #1565c0;
  }
}

:deep(.el-button.is-circle) {
  margin: 0 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// Logo上传相关样式
.logo-uploader {
  :deep() {
    .logo-preview {
      width: 120px;
      height: 120px;
      display: block;
      object-fit: contain;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
    }

    .el-upload {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      width: 120px;
      height: 120px;
    }

    .el-upload:hover {
      border-color: var(--el-color-primary);
    }

    .logo-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 120px;
      height: 120px;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.upload-buttons {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
