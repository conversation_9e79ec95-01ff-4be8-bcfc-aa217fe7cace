import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

export const useUserStore = defineStore('user', () => {
  const token = ref('')
  const userInfo = ref(null)

  // 登录
  const login = async (loginData) => {
    try {
      // 确保参数是字符串类型
      const username = String(loginData.username || '').trim()
      const password = String(loginData.password || '').trim()
      
      console.log('登录参数:', { username, password })
      
      // 确保用户名和密码不为空
      if (!username || !password) {
        console.log('用户名或密码为空:', { username, password })
        ElMessage.error('用户名和密码不能为空')
        return false
      }
      
      // 确保用户名和密码长度符合要求
      if (username.length < 5 || username.length > 16 || password.length < 5 || password.length > 16) {
        console.log('用户名或密码长度不符合要求:', { 
          usernameLength: username.length, 
          passwordLength: password.length 
        })
        ElMessage.error('用户名和密码长度必须在5-16位之间')
        return false
      }

      // 使用 URLSearchParams 发送表单数据
      const params = new URLSearchParams()
      params.append('username', username)
      params.append('password', password)
      
      const response = await request.post('/user/login', params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
      console.log('登录响应:', response)
      
      if (response.code === 0) {
        token.value = response.data
        // 登录成功后立即获取用户信息
        await getUserInfo()
        ElMessage.success('登录成功')
        return true
      } else {
        ElMessage.error(response.message || '登录失败')
        return false
      }
    } catch (error) {
      console.error('登录错误:', error)
      ElMessage.error('登录失败')
      return false
    }
  }

  // 注册
  const register = async (registerData) => {
    try {
      // 确保参数是字符串类型
      const username = String(registerData.username || '').trim()
      const password = String(registerData.password || '').trim()
      const role = String(registerData.role || 'CUSTOMER').trim()

      console.log('注册参数:', { username, password, role })
      
      // 确保用户名和密码不为空
      if (!username || !password) {
        console.log('用户名或密码为空:', { username, password })
        ElMessage.error('用户名和密码不能为空')
        return false
      }
      
      // 确保用户名和密码长度符合要求
      if (username.length < 5 || username.length > 16 || password.length < 5 || password.length > 16) {
        console.log('用户名或密码长度不符合要求:', { 
          usernameLength: username.length, 
          passwordLength: password.length 
        })
        ElMessage.error('用户名和密码长度必须在5-16位之间')
        return false
      }

      // 使用 URLSearchParams 发送表单数据
      const params = new URLSearchParams()
      params.append('username', username)
      params.append('password', password)
      params.append('role', role)
      
      const response = await request.post('/user/register', params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
      console.log('注册响应:', response)
      
      if (response.code === 0) {
        ElMessage.success('注册成功')
        return true
      } else {
        ElMessage.error(response.message || '注册失败')
        return false
      }
    } catch (error) {
      console.error('注册错误:', error)
      ElMessage.error('注册失败')
      return false
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    if (!token.value) {
      console.error('获取用户信息失败: token不存在')
      return false
    }

    try {
      console.log('开始获取用户信息，当前token:', token.value)
      const response = await request.get('/user/userInfo')
      console.log('获取用户信息响应:', response)
      
      if (response.code === 0 && response.data) {
        // 确保用户ID字段正确
        const userData = response.data
        if (userData.userId) {
          userData.id = userData.userId // 添加id字段以兼容现有代码
        }
        userInfo.value = userData
        console.log('用户信息获取成功:', userInfo.value)
        return true
      } else {
        console.error('获取用户信息失败: 返回数据格式不正确', response)
        ElMessage.error(response.message || '获取用户信息失败')
        return false
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      if (error.response?.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        logout()
        return false
      }
      ElMessage.error('获取用户信息失败')
      return false
    }
  }

  // 退出登录
  const logout = () => {
    token.value = ''
    userInfo.value = null
    // 清除本地存储
    localStorage.removeItem('user-store')
    ElMessage.success('退出成功')
  }

  // 检查登录状态
  const checkLogin = () => {
    if (!token.value) {
      ElMessage.warning('请先登录')
      return false
    }
    return true
  }

  // 检查用户角色权限
  const hasRole = (requiredRoles) => {
    if (!userInfo.value?.role) {
      return false
    }

    if (Array.isArray(requiredRoles)) {
      return requiredRoles.includes(userInfo.value.role)
    }

    return userInfo.value.role === requiredRoles
  }

  // 检查是否为管理员
  const isAdmin = () => {
    return userInfo.value?.role === 'ADMIN'
  }

  // 检查是否为生产商
  const isManufacturer = () => {
    return userInfo.value?.role === 'MANUFACTURER'
  }

  // 检查是否为普通用户
  const isCustomer = () => {
    return userInfo.value?.role === 'CUSTOMER' || !userInfo.value?.role
  }

  // 检查是否可以管理商品（生产商或管理员）
  const canManageProducts = () => {
    return hasRole(['MANUFACTURER', 'ADMIN'])
  }

  // 检查是否可以管理分类（仅管理员）
  const canManageCategories = () => {
    return hasRole('ADMIN')
  }

  // 检查权限并显示错误信息
  const checkPermission = (requiredRoles, errorMessage = '权限不足') => {
    if (!checkLogin()) {
      return false
    }

    if (!hasRole(requiredRoles)) {
      ElMessage.error(errorMessage)
      return false
    }

    return true
  }

  return {
    token,
    userInfo,
    login,
    register,
    getUserInfo,
    logout,
    checkLogin,
    hasRole,
    isAdmin,
    isManufacturer,
    isCustomer,
    canManageProducts,
    canManageCategories,
    checkPermission
  }
}, {
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['token', 'userInfo']
  }
}) 
